{"name": "genspark-ai-chat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.2.1", "katex": "^0.16.9", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-katex": "^3.0.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^5.8.0", "rehype-katex": "^7.0.0", "remark-math": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/typography": "^0.5.10", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.1"}}