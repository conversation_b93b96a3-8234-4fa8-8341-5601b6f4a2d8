# GenSparkAI 🚀

An intelligent AI-powered chat assistant built with React, TypeScript, and Google's Gemini AI. GenSparkAI provides comprehensive assistance for code development, mathematical problem-solving, writing tasks, and image analysis.

[Edit in StackBlitz next generation editor ⚡️](https://stackblitz.com/~/github.com/Jorgepaulo12/gensparkai)

## ✨ Features

- **🔧 Code Development**: Get help with programming in multiple languages with instant code generation and debugging assistance
- **📊 Mathematical Solutions**: Solve complex equations with step-by-step explanations and mathematical reasoning
- **✍️ Writing Assistant**: Create professional emails, documents, and content with AI-powered writing support
- **🖼️ Image Analysis**: Upload and analyze images with detailed AI-powered insights and descriptions
- **🌙 Dark/Light Mode**: Toggle between dark and light themes for comfortable viewing
- **💬 Real-time Chat**: Interactive chat interface with typewriter effect for responses
- **📱 Responsive Design**: Optimized for desktop and mobile devices

## 🛠️ Technologies Used

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Lucide React Icons
- **AI Integration**: Google Generative AI (Gemini)
- **Markdown Support**: React Markdown with syntax highlighting
- **Math Rendering**: KaTeX for mathematical expressions

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Jorgepaulo12/gensparkai.git
cd gensparkai
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build
- `npm run lint` - Run ESLint for code quality checks

## 🔧 Configuration

The application uses Google's Gemini AI API. Make sure to configure your API key in the `src/services/gemini.ts` file for full functionality.

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ChatInput.tsx   # Message input component
│   ├── ChatMessage.tsx # Message display component
│   ├── FeatureCards.tsx # Feature showcase cards
│   ├── FeatureCard.tsx # Individual feature card
│   ├── ThemeToggle.tsx # Dark/light mode toggle
│   └── TypewriterText.tsx # Typewriter effect component
├── hooks/              # Custom React hooks
├── services/           # API services
│   └── gemini.ts      # Google Gemini AI integration
├── types/             # TypeScript type definitions
└── App.tsx           # Main application component
```

## 🎨 Features in Detail

### Code Development
- Multi-language programming support
- Code generation and optimization
- Debugging assistance
- Best practices recommendations

### Mathematical Solutions
- Equation solving with step-by-step explanations
- Graph plotting and analysis
- Statistical calculations
- Mathematical concept explanations

### Writing Assistant
- Professional email composition
- Document creation and editing
- Grammar and style suggestions
- Content optimization

### Image Analysis
- Image content description
- Object detection and identification
- Scene analysis
- Text extraction from images

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Google Gemini AI for providing the AI capabilities
- React and TypeScript communities for excellent documentation
- Tailwind CSS for the beautiful styling system