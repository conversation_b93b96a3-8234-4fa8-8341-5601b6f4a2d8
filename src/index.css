@tailwind base;
@tailwind components;
@tailwind utilities;

.prose {
  max-width: none;
}

.dark .prose {
  color: #e5e7eb;
}

.dark .prose a {
  color: #93c5fd;
}

.dark .prose strong {
  color: #f3f4f6;
}

.prose pre {
  background-color: #282a36;
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.dark .prose pre {
  background-color: #1f2937;
}

.prose code {
  background-color: #f1f5f9;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.dark .prose code {
  background-color: #374151;
  color: #e5e7eb;
}

.prose img {
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.katex-display {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 1rem 0;
  background-color: rgba(229, 231, 235, 0.1);
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.dark .katex {
  color: #e5e7eb;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.prose th {
  background-color: #f3f4f6;
  font-weight: 600;
}

.dark .prose th {
  background-color: #374151;
}

.prose td,
.prose th {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
}

.dark .prose td,
.dark .prose th {
  border-color: #4b5563;
}

.prose blockquote {
  border-left-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  padding: 1rem;
  margin: 1rem 0;
}

.dark .prose blockquote {
  background-color: rgba(59, 130, 246, 0.05);
}