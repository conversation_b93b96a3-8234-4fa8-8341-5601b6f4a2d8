import React from 'react';
import { Code2, Image as ImageIcon, Calculator, MessageSquare, Sparkles } from 'lucide-react';
import { FeatureCard } from './FeatureCard';

interface FeatureCardsProps {
  onSelectFeature: (prompt: string) => void;
}

export function FeatureCards({ onSelectFeature }: FeatureCardsProps) {
  const features = [
    {
      icon: Code2,
      title: 'Code Development',
      description: '🚀 Create perfect code in seconds! Support for multiple programming languages.',
      prompt: 'I need help creating a JavaScript function',
      gradient: 'from-blue-500 to-indigo-500'
    },
    {
      icon: Calculator,
      title: 'Mathematical Solutions',
      description: '📊 Solve complex equations instantly! Step-by-step explanations.',
      prompt: 'How to solve quadratic equations?',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: MessageSquare,
      title: 'Writing Assistant',
      description: '✍️ Professional texts in an instant! Perfect for emails and documents.',
      prompt: 'Help me write a professional email',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: ImageIcon,
      title: 'Image Analysis',
      description: '🖼️ Understand images with AI! Detailed analysis and precise insights.',
      prompt: 'I will send an image for analysis',
      gradient: 'from-orange-500 to-red-500'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto p-6">
      <div className="col-span-full text-center mb-8">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Sparkles className="w-8 h-8 text-blue-500" />
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Discover the Power of AI
          </h2>
        </div>
        <p className="text-gray-600 dark:text-gray-300">
          Powered by GenSpark - Transforming your ideas into reality
        </p>
      </div>
      {features.map((feature) => (
        <FeatureCard
          key={feature.title}
          {...feature}
          onClick={() => onSelectFeature(feature.prompt)}
        />
      ))}
    </div>
  );
}